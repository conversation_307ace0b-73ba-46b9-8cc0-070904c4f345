"""
LLM連接池優化模組
用於管理OpenAI API調用，減少重複初始化和提高並行處理效率
"""

import asyncio
import time
from typing import Dict, Any, List, Optional
from concurrent.futures import ThreadPoolExecutor
import threading

class LLMConnectionPool:
    """LLM連接池，用於管理和重用LLM實例"""
    
    def __init__(self, max_workers: int = 5):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.semaphore = asyncio.Semaphore(max_workers)
        self.request_count = 0
        self.total_time = 0
        self.lock = threading.Lock()
        
    async def call_llm_async(self, llm_instance, messages: List[Dict[str, Any]], timeout: int = 60) -> Optional[Dict[str, Any]]:
        """異步調用LLM，帶有timeout控制"""
        async with self.semaphore:
            try:
                start_time = time.time()
                
                # 在線程池中執行LLM調用
                loop = asyncio.get_event_loop()
                result = await asyncio.wait_for(
                    loop.run_in_executor(
                        self.executor,
                        lambda: llm_instance.chat_json(messages)
                    ),
                    timeout=timeout
                )
                
                end_time = time.time()
                with self.lock:
                    self.request_count += 1
                    self.total_time += (end_time - start_time)
                
                return result
                
            except asyncio.TimeoutError:
                print(f"LLM調用超時 ({timeout}秒)")
                return None
            except Exception as e:
                print(f"LLM調用錯誤: {e}")
                return None
    
    def get_stats(self) -> Dict[str, float]:
        """獲取統計信息"""
        with self.lock:
            if self.request_count > 0:
                avg_time = self.total_time / self.request_count
            else:
                avg_time = 0
            
            return {
                "total_requests": self.request_count,
                "total_time": self.total_time,
                "average_time": avg_time
            }
    
    def close(self):
        """關閉連接池"""
        self.executor.shutdown(wait=True)

# 全局連接池實例
_llm_pool = None

def get_llm_pool() -> LLMConnectionPool:
    """獲取全局LLM連接池實例"""
    global _llm_pool
    if _llm_pool is None:
        _llm_pool = LLMConnectionPool(max_workers=5)
    return _llm_pool

async def batch_llm_calls(llm_instances_and_messages: List[tuple], timeout: int = 60) -> List[Optional[Dict[str, Any]]]:
    """批量並行調用LLM"""
    pool = get_llm_pool()
    
    tasks = []
    for llm_instance, messages in llm_instances_and_messages:
        task = pool.call_llm_async(llm_instance, messages, timeout)
        tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 處理異常結果
    processed_results = []
    for result in results:
        if isinstance(result, Exception):
            print(f"批量LLM調用中發生異常: {result}")
            processed_results.append(None)
        else:
            processed_results.append(result)
    
    return processed_results

class OptimizedLLMProcessor:
    """優化的LLM處理器，用於批量處理新聞項目"""
    
    def __init__(self, api_key: str, max_concurrent: int = 3):
        self.api_key = api_key
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.pool = get_llm_pool()
    
    async def process_news_batch(self, news_items: List[Dict[str, Any]], 
                                process_func, batch_size: int = 5) -> List[Dict[str, Any]]:
        """批量處理新聞項目"""
        results = []
        
        # 分批處理以避免過載
        for i in range(0, len(news_items), batch_size):
            batch = news_items[i:i + batch_size]
            
            # 並行處理當前批次
            tasks = []
            for item in batch:
                task = self._process_single_item(item, process_func)
                tasks.append(task)
            
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 處理結果
            for j, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    print(f"處理第 {i+j} 個新聞項目時發生異常: {result}")
                    # 使用原始項目作為備用
                    results.append(batch[j])
                else:
                    results.append(result)
            
            # 批次間稍作延遲，避免API限制
            if i + batch_size < len(news_items):
                await asyncio.sleep(0.5)
        
        return results
    
    async def _process_single_item(self, item: Dict[str, Any], process_func) -> Dict[str, Any]:
        """處理單個新聞項目"""
        async with self.semaphore:
            try:
                return await process_func(item, self.api_key, self.pool)
            except Exception as e:
                print(f"處理新聞項目時發生錯誤: {e}")
                return item

def cleanup_llm_pool():
    """清理全局LLM連接池"""
    global _llm_pool
    if _llm_pool is not None:
        _llm_pool.close()
        _llm_pool = None
